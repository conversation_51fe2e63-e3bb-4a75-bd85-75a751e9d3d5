/* Custom Dark Mode Styles */
html.dark {
  /* Custom dark background colors */
  --el-bg-color: #1a1a1a;
  --el-bg-color-page: #0a0a0a;
  --el-bg-color-overlay: #1d1e1f;
  
  /* Custom text colors */
  --el-text-color-primary: #e5eaf3;
  --el-text-color-regular: #cfd3dc;
  --el-text-color-secondary: #a3a6ad;
  
  /* Custom border colors */
  --el-border-color: #4c4d4f;
  --el-border-color-light: #414243;
  
  /* Custom card colors */
  --el-card-bg-color: #1d1e1f;
  --el-card-border-color: #4c4d4f;
  
  /* Custom table colors */
  --el-table-bg-color: #1d1e1f;
  --el-table-tr-bg-color: #1d1e1f;
  --el-table-header-bg-color: #2b2b2b;
  --el-table-border-color: #4c4d4f;
  
  /* Custom form colors */
  --el-input-bg-color: #2b2b2b;
  --el-input-border-color: #4c4d4f;
  --el-input-text-color: #e5eaf3;
  
  /* Custom button colors */
  --el-button-bg-color: #2b2b2b;
  --el-button-border-color: #4c4d4f;
  --el-button-text-color: #e5eaf3;
  
  /* Custom pagination colors */
  --el-pagination-bg-color: #1d1e1f;
  --el-pagination-button-bg-color: #2b2b2b;
  --el-pagination-button-color: #e5eaf3;
  
  /* Custom dropdown colors */
  --el-dropdown-menu-bg-color: #2b2b2b;
  --el-dropdown-menu-item-color: #e5eaf3;
  --el-dropdown-menu-item-hover-bg-color: #3a3a3a;
}

/* Dark mode specific styles for the application */
html.dark .ddzl-report-page {
  background-color: var(--el-bg-color-page);
  color: var(--el-text-color-primary);
}

html.dark .page-header h1 {
  color: var(--el-text-color-primary);
}

html.dark .box-card {
  background-color: var(--el-card-bg-color);
  border-color: var(--el-card-border-color);
}

html.dark .pagination-container {
  background-color: var(--el-card-bg-color);
  border-top-color: var(--el-border-color);
}

/* Dark mode for header */
html.dark .container-head {
  background-color: var(--el-bg-color-overlay);
}

html.dark .title {
  color: var(--el-text-color-primary);
}

/* Dark mode for table */
html.dark .el-table {
  background-color: var(--el-table-bg-color);
  color: var(--el-text-color-primary);
}

html.dark .el-table th {
  background-color: var(--el-table-header-bg-color);
  color: var(--el-text-color-primary);
}

html.dark .el-table td {
  background-color: var(--el-table-tr-bg-color);
  color: var(--el-text-color-primary);
}

html.dark .el-table--striped .el-table__body tr.el-table__row--striped td {
  background-color: var(--el-bg-color-overlay);
}

/* Dark mode for form elements */
html.dark .el-input__wrapper {
  background-color: var(--el-input-bg-color);
  border-color: var(--el-input-border-color);
}

html.dark .el-input__inner {
  color: var(--el-input-text-color);
}

html.dark .el-select .el-input__wrapper {
  background-color: var(--el-input-bg-color);
  border-color: var(--el-input-border-color);
}

/* Dark mode for date picker */
html.dark .el-date-editor {
  background-color: var(--el-input-bg-color);
  border-color: var(--el-input-border-color);
}

/* Dark mode for buttons */
html.dark .el-button {
  background-color: var(--el-button-bg-color);
  border-color: var(--el-button-border-color);
  color: var(--el-button-text-color);
}

/* Dark mode for pagination */
html.dark .el-pagination {
  background-color: var(--el-pagination-bg-color);
}

html.dark .el-pagination .btn-prev,
html.dark .el-pagination .btn-next {
  background-color: var(--el-pagination-button-bg-color);
  color: var(--el-pagination-button-color);
}

html.dark .el-pagination .el-pager li {
  background-color: var(--el-pagination-button-bg-color);
  color: var(--el-pagination-button-color);
}

html.dark .el-pagination .el-pager li.is-active {
  background-color: var(--el-color-primary);
  color: white;
} 