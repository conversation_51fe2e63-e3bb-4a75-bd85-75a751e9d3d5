import { createRouter, createWebHistory } from "vue-router";

const router = createRouter({
  history: createWebHistory(),
  routes: [
    {
      path: "/",
      redirect: "/login",
    },
    {
      path: "/login",
      name: "Login",
      component: () => import("@/pages/LoginPage.vue"),
      meta: { requiresAuth: false, title: "Login" },
    },

    {
      path: "/",
      component: () => import("@/components/layout/AppLayout.vue"),
      meta: { requiresAuth: true },
      children: [
        {
          path: "ddzl-report",
          name: "DDZL Report",
          component: () => import("@/pages/DdzlReport.vue"),
          meta: { title: "DDZL Report" },
        },
        {
          path: "/xxzl-model",
          name: "XXZL Model",
          component: () => import("@/pages/XxzlModelPage.vue"),
          meta: { title: "XXZL Model" },
        },
        {
          path: "/product-plan-tracking",
          name: "Product Plan Tracking",
          component: () => import("@/pages/ProductPlanTrackingPage.vue"),
          meta: { title: "Product Plan Tracking" },
        },
      ],
    },
  ],
});

// Navigation guard for authentication
router.beforeEach((to, from, next) => {
  const isAuthenticated =
    localStorage.getItem("auth-token") && localStorage.getItem("user-data");

  if (to.path === "/login" && isAuthenticated) {
    // Nếu đã đăng nhập mà vào /login thì chuyển về /ddzl-report
    next({ name: "DDZL Report" });
  } else if (to.meta.requiresAuth && !isAuthenticated) {
    // Nếu chưa đăng nhập mà vào trang cần auth thì chuyển về /login
    next({ name: "Login" });
  } else {
    next();
  }
});

// Set document.title after each route change
router.afterEach((to) => {
  const baseTitle = "ERP Workflow";
  if (to.meta && to.meta.title) {
    document.title = `${to.meta.title} - ${baseTitle}`;
  } else {
    document.title = baseTitle;
  }
});

export default router;
