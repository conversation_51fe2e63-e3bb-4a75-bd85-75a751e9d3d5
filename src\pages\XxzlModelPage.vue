<template>
  <div class="xxzl-model-page">
    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>Filter</span>
          <el-button
            type="primary"
            @click="isDialogOpen = true"
            size="small"
            style="float: right; margin-left: 12px"
          >
            <el-icon style="margin-right: 4px"><Plus /></el-icon>
            Add new
          </el-button>
        </div>
      </template>
      <el-form :inline="true" @submit.prevent="onSearch">
        <el-form-item label="Article">
          <el-select
            v-model="search.article"
            clearable
            filterable
            placeholder="Select Article"
            style="width: 150px"
          >
            <el-option
              v-for="a in store.articles"
              :key="a"
              :label="a"
              :value="a"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="Model">
          <el-select
            v-model="search.model"
            clearable
            filterable
            placeholder="Select Model"
            style="width: 150px"
          >
            <el-option
              v-for="m in store.modelNames"
              :key="m"
              :label="m"
              :value="m"
            />
          </el-select>
        </el-form-item>
        <el-form-item>
          <el-button type="primary" @click="onSearch">Search</el-button>
          <el-button @click="onReset">Reset</el-button>
        </el-form-item>
      </el-form>
    </el-card>

    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>Result ({{ store.paginated?.totalRows || 0 }} records)</span>
        </div>
      </template>
      <el-skeleton :loading="store.loadingList" animated :rows="8">
        <template #default>
          <div class="table-container">
            <el-scrollbar height="400px">
              <template
                v-if="store.paginated?.data && store.paginated.data.length > 0"
              >
                <el-table
                  :data="store.paginated.data"
                  stripe
                  style="width: 100%"
                >
                  <el-table-column prop="article" label="Article" />
                  <el-table-column prop="model" label="Model" />
                </el-table>
              </template>
              <el-empty v-else :image-size="200" />
            </el-scrollbar>
          </div>
          <!-- Pagination -->
          <div
            class="pagination-container"
            v-if="store.paginated && store.paginated.totalRows > 0"
          >
            <el-pagination
              :current-page="store.paginated.page"
              :page-size="store.paginated.pageSize"
              :total="store.paginated.totalRows"
              @current-change="onPageChange"
              layout="total, prev, pager, next, jumper"
              :hide-on-single-page="false"
            />
          </div>
        </template>
      </el-skeleton>
    </el-card>

    <!-- Dialog Add new -->
    <el-dialog
      v-model="isDialogOpen"
      title="Add new data"
      width="400px"
      :close-on-click-modal="false"
    >
      <el-form
        :model="dialogModel"
        :rules="formRules"
        ref="formRef"
        label-width="80px"
        @submit.prevent="onDialogAdd"
      >
        <el-form-item label="Article" prop="article">
          <el-input
            v-model="dialogModel.article"
            autocomplete="off"
            placeholder="Enter article code"
          />
        </el-form-item>
        <el-form-item label="Model" prop="model">
          <el-input
            v-model="dialogModel.model"
            autocomplete="off"
            placeholder="Enter model name"
          />
        </el-form-item>
      </el-form>
      <template #footer>
        <el-button @click="closeDialog">Cancel</el-button>
        <el-button type="primary" :loading="isLoading" @click="onDialogAdd"
          >Add</el-button
        >
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from "vue";
import { useXxzlModelStore } from "@/stores/xxzlModel";
import { Plus } from "@element-plus/icons-vue";
import type { FormInstance, FormRules } from "element-plus";
import { ElMessage } from "element-plus";

const isLoading = ref(false);
const store = useXxzlModelStore();
const search = ref({ article: "", model: "", page: 1, pageSize: 10 });

const isDialogOpen = ref(false);
const dialogModel = ref({ article: "", model: "" });
const formRef = ref<FormInstance>();

// Form validation rules
const formRules: FormRules = {
  article: [
    { required: true, message: "Article is required", trigger: "blur" },
  ],
  model: [{ required: true, message: "Model is required", trigger: "blur" }],
};

onMounted(() => {
  store.getDistinctArticles();
  store.getDistinctModels();
  store.getModelsPaginated(search.value);
});

function onSearch() {
  search.value.page = 1;
  store.getModelsPaginated(search.value);
}

function onReset() {
  search.value.article = "";
  search.value.model = "";
  search.value.page = 1;
  store.getModelsPaginated(search.value);
}

function onPageChange(page: number) {
  search.value.page = page;
  store.getModelsPaginated(search.value);
}

function closeDialog() {
  isDialogOpen.value = false;
  formRef.value?.resetFields();
  dialogModel.value = { article: "", model: "" };
}

async function onDialogAdd() {
  if (!formRef.value) return;
  isLoading.value = true;
  try {
    await formRef.value.validate();

    await store.addModel({ ...dialogModel.value });
    // Refresh lại cả filter options sau khi thêm mới
    await Promise.all([store.getDistinctArticles(), store.getDistinctModels()]);
    await store.getModelsPaginated(search.value);

    closeDialog();
  } catch (error) {
    ElMessage.error("An error occurred. Please try again!");
  } finally {
    isLoading.value = false;
  }
}
</script>

<style scoped>
.xxzl-model-page {
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.box-card {
  margin-top: 20px;
  flex-shrink: 0;
}

.box-card:last-child {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

/* Table container with scrolling */
.table-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

:deep(.el-card__body) {
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

:deep(.el-table) {
  flex: 1;
  overflow: auto;
}

:deep(.el-table__body-wrapper) {
  overflow-y: auto;
  max-height: calc(100vh - 400px);
}

/* Pagination container */
.pagination-container {
  margin-top: 20px;
  text-align: right;
  flex-shrink: 0;
  padding: 10px 0;
  background: #fff;
  border-radius: 0 0 8px 8px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.03);
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .xxzl-model-page {
    padding: 10px;
  }

  .card-header {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }

  .pagination-container {
    text-align: center;
    padding: 8px 0;
  }

  :deep(.el-table__body-wrapper) {
    max-height: calc(100vh - 300px);
  }
}

/* Scrollbar styling */
:deep(.el-scrollbar__bar) {
  width: 8px;
  background-color: transparent;
}

:deep(.el-scrollbar__thumb) {
  background-color: #c0c4cc;
  border-radius: 4px;
}

:deep(.el-scrollbar__wrap) {
  scroll-behavior: smooth;
}
</style>
