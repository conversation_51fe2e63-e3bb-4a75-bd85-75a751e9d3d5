/**
 * TypeScript definitions for DDZL API
 * Copy this file to your frontend project for type safety
 */

// API Response wrapper
export interface ApiResponse<T> {
  code: number;
  data: T;
  message: string;
}

// DDZL Record structure - Updated to match actual API response
export interface DDZLRecord {
  DDBH: string;        // Order number
  GSBH: string;        // Company code
  XieXing: string;     // Shoe type
  SheHao: string;      // Shoe number
  ARTICLE: string;     // Product article code
  CCGB: string;        // Size standard
  KHBH: string;        // Customer code
  BB: string | null;   // Version
  KHPO: string;        // Customer PO
  Version: number;     // Version number
  Trader: string | null;      // Trader name
  TraderPO: string | null;    // Trader PO
  DDLB: string;        // Order type
  DDZT: string;        // Order status
  CPLB: string;        // Product type
  BZFS: string | null;        // Package method
  Dest: string;        // Destination
  DDGB: string;        // Order standard
  DDRQ: string;        // Order date (ISO 8601)
  JYTJ: string | null;        // Trade terms
  FKTJ: string | null;        // Payment terms
  ShipDate: string;    // Ship date (ISO 8601)
  Pairs: number;       // Number of pairs
  Totals: number | null;      // Total quantity
  ZLBH: string;        // Material code
  GSDH: string;        // Company number
  CFNO: string;        // CF number
  USERID: string;      // User ID
  USERDATE: string;    // User date (ISO 8601)
  DDCZ: string | null;        // Order operation
  DDPACKSM: string | null;    // Package description
  LABELCHARGE: string | null; // Label charge
  Gender: string;      // Gender
  YN: string;          // Yes/No flag
  OrderMode: string | null;   // Order mode
  OKDate: string;      // OK date (ISO 8601)
  BUYNO: string;       // Buy number
  Pairs2: number | null;      // Pairs 2
  Balance2: number | null;    // Balance amount
  Flag: string | null;        // Flag
  RYTYPE: string | null;      // RY type
}

// Query parameters for filtering
export interface DDZLQueryParams {
  buynoPrefix?: string;  // Filter by first 6 characters of BUYNO
  fromDate?: string;     // Start date (YYYY-MM-DD)
  toDate?: string;       // End date (YYYY-MM-DD)
  ddbh?: string;         // Filter DDBH with LIKE pattern
  article?: string;      // Exact match for Article
  khpo?: string;         // Filter by KHPO (Customer PO)
  page?: number;         // Page number (starts from 1, default: 1)
  pageSize?: number;     // Records per page (default: 50, max: 1000)
}

// Pagination response structure
export interface PaginatedResponse<T> {
  data: T[];
  totalRows: number;
  totalPages: number;
  page: number;
  pageSize: number;
  hasNext: boolean;
  hasPrev: boolean;
  message: string;
  success: boolean;
}

// API response types
export type DDZLListResponse = ApiResponse<DDZLRecord[]>;
export type DDZLPaginatedResponse = ApiResponse<PaginatedResponse<DDZLRecord>>;

// API service interface
export interface DDZLApiService {
  getAllRecords(): Promise<DDZLRecord[]>;
  getFilteredRecords(params: DDZLQueryParams): Promise<DDZLRecord[]>;
}

// Error response
export interface ApiError {
  code: number;
  data: null;
  message: string;
}

// Utility types for common filters
export interface DateRangeFilter {
  fromDate: string;
  toDate:string;
}

export interface BuynoFilter {
  buynoPrefix: string;
}

export interface ArticleFilter {
  article: string;
}

export interface DdbhFilter {
  ddbh: string;
}

export interface KhpoFilter {
  khpo: string;
}

// Combined filter type
export type DDZLFilter = Partial<DateRangeFilter & BuynoFilter & ArticleFilter & DdbhFilter & KhpoFilter>;

// API client configuration
export interface ApiConfig {
  baseURL: string;
  timeout?: number;
  headers?: Record<string, string>;
}

const urlIp = `${import.meta.env.VITE_BACKEND_URL}`;

// Default API configuration
export const DEFAULT_API_CONFIG: ApiConfig = {
  baseURL: urlIp,
  timeout: 100000,
  headers: {
    'Content-Type': 'application/json',
  },
};

// Validation helpers
export const isValidDateFormat = (date: string): boolean => {
  return /^\d{4}-\d{2}-\d{2}$/.test(date);
};

export const isValidBuynoPrefix = (prefix: string): boolean => {
  return prefix.length === 6 && /^\d{6}$/.test(prefix);
};

// Date formatting utilities
export const formatDateForApi = (date: Date): string => {
  return date.toISOString().split('T')[0];
};

export const parseApiDate = (dateString: string): Date => {
  return new Date(dateString);
};

// Example usage in comments:
/*
// Usage example:
import { DDZLRecord, DDZLQueryParams, DDZLApiService } from './ddzl-api.types';

const apiService: DDZLApiService = {
  async getAllRecords(): Promise<DDZLRecord[]> {
    const response = await fetch('http://localhost:8081/api/ddzl');
    const data: DDZLListResponse = await response.json();
    return data.data;
  },

  async getFilteredRecords(params: DDZLQueryParams): Promise<DDZLRecord[]> {
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value) searchParams.append(key, value);
    });
    
    const response = await fetch(`http://localhost:8081/api/ddzl?${searchParams}`);
    const data: DDZLListResponse = await response.json();
    return data.data;
  }
};

// Filter examples:
const filters: DDZLQueryParams = {
  buynoPrefix: '202502',
  fromDate: '2025-02-01',
  toDate: '2025-02-28',
  ddbh: 'Y2505',
  article: '164225C'
};

const records = await apiService.getFilteredRecords(filters);
*/ 