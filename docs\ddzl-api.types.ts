/**
 * TypeScript definitions for DDZL API
 * Copy this file to your frontend project for type safety
 */

// API Response wrapper
export interface ApiResponse<T> {
  code: number;
  data: T;
  message: string;
}

// DDZL Record structure
export interface DDZLRecord {
  ddbh: string;        // Order number
  gsbh: string;        // Company code
  xiexing: string;     // Shoe type
  shehao: string;      // Shoe number
  article: string;     // Product article code
  ccgb: string;        // Size standard
  khbh: string;        // Customer code
  bb: string;          // Version
  khpo: string;        // Customer PO
  version: number;     // Version number
  trader: string;      // Trader name
  traderpo: string;    // Trader PO
  ddlb: string;        // Order type
  ddzt: string;        // Order status
  cplb: string;        // Product type
  bzfs: string;        // Package method
  dest: string;        // Destination
  ddgb: string;        // Order standard
  ddrq: string;        // Order date (ISO 8601)
  jytj: string;        // Trade terms
  fktj: string;        // Payment terms
  shipDate: string;    // Ship date (ISO 8601)
  pairs: number;       // Number of pairs
  totals: number;      // Total quantity
  zlbh: string;        // Material code
  gsdh: string;        // Company number
  cfno: string;        // CF number
  userid: string;      // User ID
  userdate: string;    // User date (ISO 8601)
  ddcz: string;        // Order operation
  ddpacksm: string;    // Package description
  labelcharge: string; // Label charge
  gender: string;      // Gender
  yn: string;          // Yes/No flag
  ordermode: string;   // Order mode
  okdate: string;      // OK date (ISO 8601)
  buyno: string;       // Buy number
  pairs2: number;      // Pairs 2
  balance2: number;    // Balance amount
  flag: string;        // Flag
  rytype: string;      // RY type
}

// Query parameters for filtering (no pagination)
export interface DDZLQueryParams {
  buynoPrefix?: string;  // Filter by first 6 characters of BUYNO
  fromDate?: string;     // Start date (YYYY-MM-DD)
  toDate?: string;       // End date (YYYY-MM-DD)
  ddbh?: string;         // Filter DDBH with LIKE pattern
  article?: string;      // Exact match for Article
}

// Query parameters for filtering with pagination
export interface DDZLPaginatedQueryParams extends DDZLQueryParams {
  page?: number;         // Page number (starts from 1, default: 1)
  pageSize?: number;     // Records per page (default: 50, max: 1000)
}

// Paginated response structure
export interface DDZLPaginatedData {
  data: DDZLRecord[];
  totalRows: number;
  totalPages: number;
  page: number;
  pageSize: number;
  hasNext: boolean;
  hasPrev: boolean;
  message: string;
  success: boolean;
}

// API response types
export type DDZLListResponse = ApiResponse<DDZLRecord[]>;
export type DDZLPaginatedResponse = ApiResponse<DDZLPaginatedData>;

// API service interface
export interface DDZLApiService {
  getAllRecords(): Promise<DDZLRecord[]>;
  getFilteredRecords(params: DDZLQueryParams): Promise<DDZLRecord[]>;
  getPaginatedRecords(params: DDZLPaginatedQueryParams): Promise<DDZLPaginatedData>;
}

// Error response
export interface ApiError {
  code: number;
  data: null;
  message: string;
}

// Utility types for common filters
export interface DateRangeFilter {
  fromDate: string;
  toDate: string;
}

export interface BuynoFilter {
  buynoPrefix: string;
}

export interface ArticleFilter {
  article: string;
}

export interface DdbhFilter {
  ddbh: string;
}

// Combined filter type
export type DDZLFilter = Partial<DateRangeFilter & BuynoFilter & ArticleFilter & DdbhFilter>;

// API client configuration
export interface ApiConfig {
  baseURL: string;
  timeout?: number;
  headers?: Record<string, string>;
}

// Default API configuration
export const DEFAULT_API_CONFIG: ApiConfig = {
  baseURL: 'http://localhost:8081/api',
  timeout: 10000,
  headers: {
    'Content-Type': 'application/json',
  },
};

// Validation helpers
export const isValidDateFormat = (date: string): boolean => {
  return /^\d{4}-\d{2}-\d{2}$/.test(date);
};

export const isValidBuynoPrefix = (prefix: string): boolean => {
  return prefix.length === 6 && /^\d{6}$/.test(prefix);
};

// Date formatting utilities
export const formatDateForApi = (date: Date): string => {
  return date.toISOString().split('T')[0];
};

export const parseApiDate = (dateString: string): Date => {
  return new Date(dateString);
};

// Example usage in comments:
/*
// Usage example:
import { DDZLRecord, DDZLQueryParams, DDZLApiService } from './ddzl-api.types';

const apiService: DDZLApiService = {
  async getAllRecords(): Promise<DDZLRecord[]> {
    const response = await fetch('http://localhost:8081/api/ddzl');
    const data: DDZLListResponse = await response.json();
    return data.data;
  },

  async getFilteredRecords(params: DDZLQueryParams): Promise<DDZLRecord[]> {
    const searchParams = new URLSearchParams();
    Object.entries(params).forEach(([key, value]) => {
      if (value) searchParams.append(key, value);
    });
    
    const response = await fetch(`http://localhost:8081/api/ddzl?${searchParams}`);
    const data: DDZLListResponse = await response.json();
    return data.data;
  }
};

// Filter examples:
const filters: DDZLQueryParams = {
  buynoPrefix: '202502',
  fromDate: '2025-02-01',
  toDate: '2025-02-28',
  ddbh: 'Y2505',
  article: '164225C'
};

const records = await apiService.getFilteredRecords(filters);
*/
