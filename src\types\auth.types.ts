/**
 * Authentication types for login API
 */

// Login request interface
export interface LoginRequest {
  userID: string;
  password: string;
}

// User data interface
export interface UserData {
  userID: string;
  userName: string;
  role: string;
  token: string;
}

// Login response interface - data can be UserData or error message string
export interface LoginResponse {
  code: number;
  message: string;
  data: UserData | string | null;
}

// Authentication state interface
export interface AuthState {
  isAuthenticated: boolean;
  user: UserData | null;
  token: string | null;
  loading: boolean;
  error: string | null;
}

// Login error types
export enum LoginErrorType {
  INVALID_CREDENTIALS = 'Account does not exist or has been locked',
  WRONG_PASSWORD = 'Password is incorrect',
  SERVER_ERROR = 'Server error',
  NETWORK_ERROR = 'Lỗi kết nối mạng'
}

// API error response
export interface ApiErrorResponse {
  code: number;
  message: string;
  data: null;
} 