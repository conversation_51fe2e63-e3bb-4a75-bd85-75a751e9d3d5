# DDZL API Documentation

## Overview
RESTful API để truy vấn dữ liệu từ bảng `ddzl` với các chức năng lọc linh hoạt.

## Endpoints

### 1. GET /api/ddzl
Lấy TẤT CẢ records từ bảng ddzl với các bộ lọc tùy chọn (không phân trang).

#### Query Parameters
| Parameter | Type | Required | Description | Example |
|-----------|------|----------|-------------|---------|
| `buynoPrefix` | string | No | Lọc theo 6 ký tự đầu tiên của BUYNO | `202502` |
| `fromDate` | string | No | Ngày bắt đầu (YYYY-MM-DD) | `2025-02-01` |
| `toDate` | string | No | Ng<PERSON><PERSON> kết thúc (YYYY-MM-DD) | `2025-02-28` |
| `ddbh` | string | No | Lọc DDBH với LIKE pattern | `Y2505` |
| `article` | string | No | L<PERSON><PERSON> ch<PERSON>h xác theo Article | `164225C` |

#### Example Requests

1. **Lọc theo tất cả các điều kiện:**
```bash
GET /api/ddzl?buynoPrefix=202502&fromDate=2025-02-01&toDate=2025-02-28&ddbh=Y2505&article=164225C
```

2. **Chỉ lọc theo BUYNO prefix:**
```bash
GET /api/ddzl?buynoPrefix=202502
```

3. **Lọc theo khoảng thời gian:**
```bash
GET /api/ddzl?fromDate=2025-02-01&toDate=2025-02-28
```

4. **Lọc theo DDBH:**
```bash
GET /api/ddzl?ddbh=Y2505
```

#### Response Format
```json
{
  "code": 200,
  "data": {
    "data": [
      {
        "id": 1,
        "buyno": "202502001234",
        "userdate": "2025-02-15T10:30:00Z",
        "ddbh": "Y2505001",
        "article": "164225C",
        "createdAt": "2025-01-01T00:00:00Z",
        "updatedAt": "2025-01-01T00:00:00Z"
      }
    ],
    "message": "Retrieved 5 records successfully",
    "success": true
  },
  "message": "success"
}
```

### 2. GET /api/ddzl/:id
Lấy một record cụ thể theo ID.

#### Path Parameters
| Parameter | Type | Required | Description |
|-----------|------|----------|-------------|
| `id` | int | Yes | ID của record |

#### Example Request
```bash
GET /api/ddzl/123
```

#### Response Format
```json
{
  "code": 200,
  "data": {
    "id": 123,
    "buyno": "202502001234",
    "userdate": "2025-02-15T10:30:00Z",
    "ddbh": "Y2505001",
    "article": "164225C",
    "createdAt": "2025-01-01T00:00:00Z",
    "updatedAt": "2025-01-01T00:00:00Z"
  },
  "message": "success"
}
```

## Filter Logic

API này được thiết kế để khớp chính xác với câu truy vấn SQL Server sau:

```sql
SELECT * FROM ddzl
WHERE 1 = 1
AND SUBSTRING(BUYNO,1,6) = '202502'  --BuyNO
AND USERDATE BETWEEN '2025/02/01' AND '2025/02/28'  --Date
AND DDBH LIKE 'Y2505%'   --OrderNo
AND Article = '164225C'  --SKU
ORDER BY USERDATE DESC
```

### BUYNO Filter
- Sử dụng `SUBSTRING(BUYNO, 1, 6) = ?` để lọc theo 6 ký tự đầu tiên
- Ví dụ: `buynoPrefix=202502` → `SUBSTRING(BUYNO, 1, 6) = '202502'`

### Date Range Filter
- Sử dụng `USERDATE BETWEEN ? AND ?` để lọc theo khoảng thời gian
- Input format: `YYYY-MM-DD` (sẽ được convert sang `YYYY/MM/DD` cho SQL Server)
- Ví dụ: `fromDate=2025-02-01&toDate=2025-02-28` → `USERDATE BETWEEN '2025/02/01' AND '2025/02/28'`
- Có thể chỉ dùng `fromDate` hoặc `toDate` riêng lẻ

### DDBH Filter (OrderNo)
- Sử dụng `DDBH LIKE 'value%'` để tìm kiếm theo pattern
- Ví dụ: `ddbh=Y2505` → `DDBH LIKE 'Y2505%'`

### Article Filter (SKU)
- Sử dụng `Article = ?` để tìm kiếm chính xác
- Ví dụ: `article=164225C` → `Article = '164225C'`

### Sorting
- Kết quả được sắp xếp theo `USERDATE DESC` (mới nhất trước)

## Error Responses

### 400 Bad Request
```json
{
  "code": 400,
  "message": "Invalid request parameters: [error details]"
}
```

### 404 Not Found
```json
{
  "code": 404,
  "message": "DDZL record not found"
}
```

### 500 Internal Server Error
```json
{
  "code": 500,
  "message": "Failed to retrieve DDZL data: [error details]"
}
```

## Notes
- Tất cả các query parameters đều là tùy chọn
- Nếu không có parameter nào được cung cấp, API sẽ trả về tất cả records
- Pagination được áp dụng mặc định với pageSize=10 nếu không chỉ định
- Date format phải tuân theo ISO 8601 (YYYY-MM-DD)
