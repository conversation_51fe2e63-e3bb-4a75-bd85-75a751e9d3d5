import api from './api';
import type { DD<PERSON>LR<PERSON>ord, DDZLQueryParams, ApiResponse, PaginatedResponse, DDZLPaginatedResponse } from '@/types/ddzl-api.types';

/**
 * Fetches DDZL records based on the provided filters.
 * @param filters The query parameters to filter the results.
 * @returns A promise that resolves to an array of DDZL records.
 */
export async function getDdzlRecords(filters?: DDZLQueryParams): Promise<DDZLRecord[]> {
  try {
    console.log('Calling getDdzlRecords with filters:', filters);
    const response = await api.get<ApiResponse<DDZLRecord[]>>('/ddzl', {
      params: filters,
    });
    console.log('getDdzlRecords response:', response);
    return response.data.data;
  } catch (error) {
    console.error('Failed to fetch DDZL records:', error);
    throw error;
  }
}

/**
 * Fetches DDZL records with pagination support (RECOMMENDED for performance).
 * @param filters The query parameters to filter the results.
 * @param page Page number (starts from 1, default: 1).
 * @param pageSize Number of records per page (default: 50, max: 1000).
 * @returns A promise that resolves to paginated DDZL records.
 */
export async function getDdzlRecordsPaginated(
  filters?: Omit<DDZLQueryParams, 'page' | 'pageSize'>,
  page: number = 1,
  pageSize: number = 50
): Promise<PaginatedResponse<DDZLRecord>> {
  try {
    const params = {
      ...filters,
      page,
      pageSize,
    }; 

    const response = await api.get<DDZLPaginatedResponse>('/ddzl/paginated', {
      params,
    });
    return response.data.data;
  } catch (error) {
    throw error;
  }
}

/**
 * Fetch order numbers (DDBH) for autocomplete search
 * @param keyword - Search keyword for filtering order numbers
 * @returns Promise<string[]> - Array of order numbers
 */
export async function getOrderNumbers(keyword: string = ''): Promise<string[]> {
  try {
    const response = await api.get('/ddzl/order-numbers', {
      params: { keyword }
    });
    
    if (response.data.code === 200) {
      return response.data.data || [];
    } else {
      throw new Error(response.data.msg || 'Failed to fetch order numbers');
    }
  } catch (error: any) {
    console.error('Error fetching order numbers:', error);
    throw new Error(error.response?.data?.msg || error.message || 'Failed to fetch order numbers');
  }
}

/**
 * Fetch BuyNo prefixes (first 6 characters of BUYNO)
 * @returns Promise<string[]> - Array of BuyNo prefixes
 */
export async function getBuyNoPrefixes(): Promise<string[]> {
  try {
    const response = await api.get('/ddzl/buyno-prefixes');
    
    if (response.data.code === 200) {
      return response.data.data || [];
    } else {
      throw new Error(response.data.msg || 'Failed to fetch BuyNo prefixes');
    }
  } catch (error: any) {
    console.error('Error fetching BuyNo prefixes:', error);
    throw new Error(error.response?.data?.msg || error.message || 'Failed to fetch BuyNo prefixes');
  }
}

/**
 * Fetch articles (SKU codes)
 * @returns Promise<string[]> - Array of articles
 */
export async function getArticles(): Promise<string[]> {
  try {
    const response = await api.get('/ddzl/articles');
    
    if (response.data.code === 200) {
      return response.data.data || [];
    } else {
      throw new Error(response.data.msg || 'Failed to fetch articles');
    }
  } catch (error: any) {
    console.error('Error fetching articles:', error);
    throw new Error(error.response?.data?.msg || error.message || 'Failed to fetch articles');
  }
}

/**
 * Fetch KHPOs (Customer PO numbers)
 * @returns Promise<string[]> - Array of KHPOs
 */
export async function getKHPOs(): Promise<string[]> {
  try {
    const response = await api.get('/ddzl/khpos');
    
    if (response.data.code === 200) {
      return response.data.data || [];
    } else {
      throw new Error(response.data.msg || 'Failed to fetch KHPOs');
    }
  } catch (error: any) {
    console.error('Error fetching KHPOs:', error);
    throw new Error(error.response?.data?.msg || error.message || 'Failed to fetch KHPOs');
  }
} 