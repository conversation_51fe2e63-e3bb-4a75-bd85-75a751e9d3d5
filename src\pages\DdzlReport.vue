<template>
  <div class="ddzl-report-page">

    <el-card class="box-card">
      <template #header>
        <div class="card-header">
          <span>Filters</span>
          <el-button
            type="primary"
            @click="showFilter = !showFilter"
            size="small"
            plain
            style="float: right; margin-left: 12px"
          >
            <el-icon style="margin-right: 4px"><Filter /></el-icon>
            {{ showFilter ? "Hide Filter" : "Show Filter" }}
          </el-button>
        </div>
      </template>
      <transition name="fade">
        <div v-show="showFilter">
          <el-form :model="filters" label-position="top">
            <el-row :gutter="16" align="bottom">
              <el-col :span="3">
                <el-form-item label="BUYNO Prefix">
                  <el-select-v2
                    v-model="filters.buynoPrefix"
                    placeholder="Select BuyNo Prefix"
                    clearable
                    style="width: 150px;"
                    :loading="buyNoPrefixesLoading"
                    filterable
                    :popper-class="'buyno-prefix-select'"
                    @change="onBuyNoPrefixChange"
                    :options="buyNoPrefixOptions"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="3">
                <el-form-item label="OrderNO">
                  <el-select-v2
                    v-model="filters.ddbh"
                    placeholder="Select Order Number"
                    clearable
                    style="width: 150px;"
                    :loading="orderNumberLoading"
                    filterable
                    :popper-class="'order-number-select'"
                    @change="onOrderNumberChange"
                    :options="orderNumberSelectOptions"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="3">
                <el-form-item label="SKU">
                  <el-select-v2
                    v-model="filters.article"
                    placeholder="Select SKU"
                    clearable
                    style="width: 150px;"
                    :loading="articlesLoading"
                    filterable
                    :popper-class="'article-select'"
                    @change="onArticleChange"
                    :options="articleOptions"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="3">
                <el-form-item label="Customer Order">
                  <el-select-v2
                    v-model="filters.khpo"
                    placeholder="Select Customer PO"
                    clearable
                    style="width: 150px;"
                    :loading="khposLoading"
                    filterable
                    :popper-class="'khpo-select'"
                    @change="onKHPOChange"
                    :options="khpoOptions"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="3">
                <el-form-item label="From date">
                  <el-date-picker
                    v-model="filters.fromDate"
                    type="date"
                    placeholder="From date"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    clearable
                    style="width: 150px;"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="4">
                <el-form-item label="To date">
                  <el-date-picker
                    v-model="filters.toDate"
                    type="date"
                    placeholder="To date"
                    format="YYYY-MM-DD"
                    value-format="YYYY-MM-DD"
                    clearable
                    style="width: 150px;"
                  />
                </el-form-item>
              </el-col>
              <el-col :span="5">
                <el-form-item>
                  <el-button type="primary" @click="handleSearch" style="width: 80px;">Search</el-button>
                  <el-button @click="clearFilters" style="width: 80px;">Clear</el-button>
                  <el-button type="success" style="width: 80px;">Export</el-button>
                </el-form-item>
              </el-col>
            </el-row>
          </el-form>
        </div>
      </transition>
    </el-card>

    <el-card class="box-card" v-loading="false">
      <template #header>
        <div class="card-header">
          <span
            >Results ({{ paginationInfo.totalRows || 0 }} total records)</span
          >
        </div>
      </template>
      <el-skeleton :loading="loading" animated :rows="8">
        <template #default>
          <div class="table-container">
            <div v-if="error" class="error-message">
              <el-alert :title="error" type="error" show-icon />
            </div>
            <el-table :data="records" stripe style="width: 100%">
              <el-table-column type="expand">
                <template #default="{ row }">
                  <div style="padding: 12px; background: var(--el-bg-color, #f9f9f9); border-radius: 6px;">
                    <b>BUYNO:</b> {{ row.BUYNO }}<br>
                    <b>ARTICLE:</b> {{ row.ARTICLE }}<br>
                    <b>DDBH:</b> {{ row.DDBH }}<br>
                    <b>GSBH:</b> {{ row.GSBH }}<br>
                    <b>All Data:</b>
                    <pre style="margin: 0; background: none;">{{ row }}</pre>
                  </div>
                </template>
              </el-table-column>
              <el-table-column label="BuyNo" sortable width="275">
                <template #default="{ row }">
                  {{ row.BUYNO ? row.BUYNO.substring(0, 6) : '' }}
                </template>
              </el-table-column>
              <el-table-column prop="DDBH" label="OrderNO" sortable width="275" />
              <el-table-column prop="ARTICLE" label="SKU" sortable width="275" />
              <el-table-column prop="KHPO" label="Customer_PO" sortable width="275" />
              <el-table-column prop="DDZT" label="Split_Status" sortable width="275" />
              <el-table-column prop="Pairs" label="QTY" sortable width="275" />
              <el-table-column prop="Dest" label="Country" sortable width="275" />
            </el-table>
          </div>
          <!-- Pagination -->
          <div class="pagination-container" v-if="paginationInfo.totalPages > 0">
            <el-pagination
              v-model:current-page="currentPage"
              v-model:page-size="pageSize"
              :page-sizes="[25, 50, 100, 200]"
              :total="paginationInfo.totalRows"
              layout="total, sizes, prev, pager, next, jumper"
              @size-change="handleSizeChange"
              @current-change="handleCurrentChange"
            />
          </div>
        </template>
      </el-skeleton>
    </el-card>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, reactive, computed } from "vue";
import { getDdzlRecords, getDdzlRecordsPaginated, getOrderNumbers, getBuyNoPrefixes, getArticles, getKHPOs } from "@/services/ddzl";
import type {
  DDZLRecord,
  DDZLQueryParams,
  PaginatedResponse,
} from "@/types/ddzl-api.types";
import {
  ElCard,
  ElForm,
  ElFormItem,
  ElButton,
  ElRow,
  ElCol,
  ElDatePicker,
  ElTable,
  ElTableColumn,
  ElAlert,
  vLoading,
  ElSelectV2,
  ElPagination,
  ElSkeleton,
} from "element-plus";

function getMonthRange() {
  const now = new Date();
  const firstDay = new Date(now.getFullYear(), now.getMonth(), 1);
  const lastDay = new Date(now.getFullYear(), now.getMonth() + 1, 0);
  const toISO = (d: Date) => d.toISOString().slice(0, 10);
  return [toISO(firstDay), toISO(lastDay)];
}

const [defaultFromDate, defaultToDate] = getMonthRange();

const records = ref<DDZLRecord[]>([]);
const loading = ref(false);
const error = ref<string | null>(null);
const currentPage = ref(1);
const pageSize = ref(25);
const showFilter = ref(true);

// Autocomplete variables
const orderNumberOptions = ref<string[]>([]);
const orderNumberLoading = ref(false);

// BuyNo prefixes variables
const buyNoPrefixes = ref<string[]>([]);
const buyNoPrefixesLoading = ref(false);

// Articles variables
const articles = ref<string[]>([]);
const articlesLoading = ref(false);

// KHPO variables
const khpos = ref<string[]>([]);
const khposLoading = ref(false);

// Computed properties for select options
const articleOptions = computed(() => {
  const options = [
    { label: 'All SKUs', value: '' }
  ];
  
  articles.value.forEach(article => {
    options.push({
      label: article,
      value: article
    });
  });
  
  return options;
});

const khpoOptions = computed(() => {
  const options = [
    { label: 'All Customer POs', value: '' }
  ];
  
  khpos.value.forEach(khpo => {
    options.push({
      label: khpo,
      value: khpo
    });
  });
  
  return options;
});

const buyNoPrefixOptions = computed(() => {
  const options = [
    { label: 'All Prefixes', value: '' }
  ];
  
  buyNoPrefixes.value.forEach(prefix => {
    options.push({
      label: prefix,
      value: prefix
    });
  });
  
  return options;
});

const orderNumberSelectOptions = computed(() => {
  const options = [
    { label: 'All Order Numbers', value: '' }
  ];
  
  orderNumberOptions.value.forEach(orderNo => {
    options.push({
      label: orderNo,
      value: orderNo
    });
  });
  
  return options;
});

const paginationInfo = reactive({
  totalRows: 0,
  totalPages: 0,
  page: 1,
  pageSize: 25,
  hasNext: false,
  hasPrev: false,
});

const filters = reactive<Omit<DDZLQueryParams, "page" | "pageSize">>({
  buynoPrefix: "",
  fromDate: defaultFromDate,
  toDate: defaultToDate,
  ddbh: "",
  article: "",
  khpo: "",
});

async function fetchData() {
  loading.value = true;
  error.value = null;
  try {

    // Try paginated endpoint first
    try {
      const result: PaginatedResponse<DDZLRecord> =
        await getDdzlRecordsPaginated(
          filters,
          currentPage.value,
          pageSize.value
        );

      records.value = result.data;
      paginationInfo.totalRows = result.totalRows;
      paginationInfo.totalPages = result.totalPages;
      paginationInfo.page = result.page;
      paginationInfo.pageSize = result.pageSize;
      paginationInfo.hasNext = result.hasNext;
      paginationInfo.hasPrev = result.hasPrev;
    } catch (paginatedError) {
      console.log(
        "Paginated endpoint failed, trying non-paginated:",
        paginatedError
      );

      // Fallback to non-paginated endpoint
      const allRecords = await getDdzlRecords(filters);

      records.value = allRecords;
      paginationInfo.totalRows = allRecords.length;
      paginationInfo.totalPages = Math.ceil(allRecords.length / pageSize.value);
      paginationInfo.page = 1;
      paginationInfo.pageSize = pageSize.value;
      paginationInfo.hasNext = false;
      paginationInfo.hasPrev = false;
    }

  } catch (err: any) {
    error.value = err.message || "An unknown error occurred.";
  } finally {
    loading.value = false;
  }
}

function handleSearch() {
  currentPage.value = 1; // Reset to first page when searching
  fetchData();
}

function clearFilters() {
  filters.buynoPrefix = '';
  filters.fromDate = "";
  filters.toDate = "";
  filters.ddbh = '';
  filters.article = '';
  filters.khpo = '';
  currentPage.value = 1;
  pageSize.value = 25;
  fetchData();
}

function handleSizeChange(newSize: number) {
  pageSize.value = newSize;
  currentPage.value = 1; // Reset to first page when changing page size
  fetchData();
}

function handleCurrentChange(newPage: number) {
  currentPage.value = newPage;
  fetchData();
}

// Fetch BuyNo prefixes for dropdown
async function fetchBuyNoPrefixes() {
  buyNoPrefixesLoading.value = true;
  try {
    const results = await getBuyNoPrefixes();
    buyNoPrefixes.value = results;
  } catch (error) {
    console.error('Error fetching BuyNo prefixes:', error);
    buyNoPrefixes.value = [];
  } finally {
    buyNoPrefixesLoading.value = false;
  }
}

function onBuyNoPrefixChange() {
  currentPage.value = 1; // Reset to first page when changing BuyNo prefix
  fetchData();
}

// Fetch articles for dropdown
async function fetchArticleList() {
  articlesLoading.value = true;
  try {
    const results = await getArticles();
    articles.value = results;
  } catch (error) {
    console.error('Error fetching articles:', error);
    articles.value = [];
  } finally {
    articlesLoading.value = false;
  }
}

function onArticleChange() {
  currentPage.value = 1; // Reset to first page when changing SKU
  fetchData();
}

// Fetch KHPOs for dropdown
async function fetchKHPOs() {
  khposLoading.value = true;
  try {
    const results = await getKHPOs();
    khpos.value = results;
  } catch (error) {
    console.error('Error fetching KHPOs:', error);
    khpos.value = [];
  } finally {
    khposLoading.value = false;
  }
}

function onKHPOChange() {
  currentPage.value = 1; // Reset to first page when changing KHPO
  fetchData();
}

// Fetch order numbers for dropdown
async function fetchOrderNumbersForSelect() {
  orderNumberLoading.value = true;
  try {
    const results = await getOrderNumbers('');
    orderNumberOptions.value = results;
  } catch (error) {
    console.error('Error fetching order numbers:', error);
    orderNumberOptions.value = [];
  } finally {
    orderNumberLoading.value = false;
  }
}

function onOrderNumberChange() {
  currentPage.value = 1; // Reset to first page when changing Order Number
  fetchData();
}

onMounted(() => {
  // Đảm bảo filter luôn là tháng hiện tại khi vào page
  filters.fromDate = defaultFromDate;
  filters.toDate = defaultToDate;
  
  // Fetch BuyNo prefixes on mount
  fetchBuyNoPrefixes();
  
  // Fetch articles on mount
  fetchArticleList();
  
  // Fetch KHPOs on mount
  fetchKHPOs();
  
  // Fetch order numbers on mount
  fetchOrderNumbersForSelect();
  
  fetchData();
});
</script>

<style scoped>
.ddzl-report-page {  
  height: 100vh;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.page-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 20px;
  flex-shrink: 0;
}

.page-header h1 {
  margin: 0;
  color: var(--el-text-color-primary);
}

.box-card {
  margin-top: 20px;
  flex-shrink: 0;
}

.box-card:last-child {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  flex-shrink: 0;
}

.error-message {
  margin-bottom: 20px;
  flex-shrink: 0;
}

/* Table container with scrolling */
.table-container {
  flex: 1;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

:deep(.el-card__body) {
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

:deep(.el-table) {
  flex: 1;
  overflow: auto;
}

:deep(.el-table__body-wrapper) {
  overflow-y: auto;
  max-height: calc(
    100vh - 400px
  ); /* Adjust based on your header/filter heights */
}

/* Pagination container */
.pagination-container {
  margin-top: 20px;
  text-align: right;
  flex-shrink: 0;
  padding: 10px 0;
}

/* Responsive adjustments */
@media (max-width: 768px) {
  .ddzl-report-page {
    padding: 10px;
  }

  .page-header {
    flex-direction: column;
    gap: 10px;
    align-items: flex-start;
  }

  :deep(.el-table__body-wrapper) {
    max-height: calc(100vh - 300px);
  }
}

.fade-enter-active,
.fade-enter-from,
.fade-leave-to {
  opacity: 0;
}

/* BuyNo prefix select styles */
:deep(.buyno-prefix-select) {
  max-height: 300px;
  overflow-y: auto;
}

:deep(.buyno-prefix-select .el-select-dropdown__item) {
  font-family: 'Courier New', monospace;
  font-weight: 500;
}

/* Article select styles */
:deep(.article-select) {
  max-height: 300px;
  overflow-y: auto;
}

:deep(.article-select .el-select-dropdown__item) {
  font-family: 'Courier New', monospace;
  font-weight: 500;
}

/* KHPO select styles */
:deep(.khpo-select) {
  max-height: 300px;
  overflow-y: auto;
}

:deep(.khpo-select .el-select-dropdown__item) {
  font-family: 'Courier New', monospace;
  font-weight: 500;
}

/* Order number select styles */
:deep(.order-number-select) {
  max-height: 300px;
  overflow-y: auto;
}

:deep(.order-number-select .el-select-dropdown__item) {
  font-family: 'Courier New', monospace;
  font-weight: 500;
}
</style>
