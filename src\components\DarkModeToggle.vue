<template>
  <div class="dark-mode-toggle">
    <el-switch
      v-model="isDark"
      :active-icon="Moon"
      :inactive-icon="Sunny"
      @change="toggleDarkMode"
      inline-prompt
    />    
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted } from 'vue';
import { <PERSON>, <PERSON> } from '@element-plus/icons-vue';

const isDark = ref(false);

function toggleDarkMode(value: boolean) {
  const html = document.documentElement;
  
  if (value) {
    html.classList.add('dark');
    localStorage.setItem('dark-mode', 'true');
  } else {
    html.classList.remove('dark');
    localStorage.setItem('dark-mode', 'false');
  }
}

onMounted(() => {
  try {
    // Check for saved dark mode preference or default to light mode
    const savedDarkMode = localStorage.getItem('dark-mode');
    
    // Always start with light mode
    isDark.value = false;
    document.documentElement.classList.remove('dark');
    
    // Then apply saved preference if it exists
    if (savedDarkMode === 'true') {
      isDark.value = true;
      document.documentElement.classList.add('dark');
    } else if (savedDarkMode === 'false') {
      // Already set to false above
    } else {
      // No saved preference, set default
      localStorage.setItem('dark-mode', 'false');
    }
    
  } catch (error) {
    isDark.value = false;
    document.documentElement.classList.remove('dark');
  }
});

// Debug function - can be called from browser console
function debugDarkMode() {
 
}

// Expose debug function globally
if (typeof window !== 'undefined') {
  (window as any).debugDarkMode = debugDarkMode;
}

function clearDarkMode() {
  localStorage.removeItem('dark-mode');
  isDark.value = false;
  document.documentElement.classList.remove('dark');
}
</script>

<style scoped>
.dark-mode-toggle {
  display: flex;
  align-items: center;
  gap: 8px;
}

.dark-mode-toggle .el-switch {
  --el-switch-on-color: #409eff;
}
</style> 