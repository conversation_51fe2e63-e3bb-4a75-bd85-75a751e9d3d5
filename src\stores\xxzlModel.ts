import { defineStore } from 'pinia'
import {
  fetchModels,
  fetchModelsPaginated,
  createModel,
  fetchDistinctArticles,
  fetchDistinctModels
} from '@/services/xxzl-model'

interface PaginatedModel {
  data: { article: string; model: string }[];
  totalRows: number;
  totalPages: number;
  page: number;
  pageSize: number;
  hasNext: boolean;
  hasPrev: boolean;
  message: string;
  success: boolean;
}

export const useXxzlModelStore = defineStore('xxzlModel', {
  state: () => ({
    models: [] as { article: string; model: string }[],
    paginated: null as PaginatedModel | null,
    articles: [] as string[],
    modelNames: [] as string[],
    loadingList: false,
    loadingAdd: false,
    error: null as any
  }),
  actions: {
    async getModels(params?: Record<string, any>) {
      this.loadingList = true
      try {
        const res = await fetchModels(params)
        this.models = res.data.data
      } catch (e) {
        this.error = e
      } finally {
        this.loadingList = false
      }
    },
    async getModelsPaginated(params?: Record<string, any>) {
      this.loadingList = true
      try {
        const res = await fetchModelsPaginated(params)
        this.paginated = res.data.data
      } catch (e) {
        this.error = e
      } finally {
        this.loadingList = false
      }
    },
    async addModel(data: { article: string; model: string }) {
      this.loadingAdd = true
      try {
        await createModel(data)
      } catch (e) {
        this.error = e
      } finally {
        this.loadingAdd = false
      }
    },
    async getDistinctArticles() {
      const res = await fetchDistinctArticles()
      this.articles = res.data.data
    },
    async getDistinctModels() {
      const res = await fetchDistinctModels()
      this.modelNames = res.data.data
    }
  }
}) 