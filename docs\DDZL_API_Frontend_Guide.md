# DDZL API - Frontend Integration Guide

## 📋 Overview
API để truy vấn dữ liệu từ bảng `ddzl` với khả năng lọc linh hoạt. API trả về **TẤT CẢ** kết quả phù hợp (không phân trang).

**Base URL:** `http://localhost:8081/api`

---

## 🚀 Endpoints

### 1. GET /api/ddzl
Lấy danh sách **TẤT CẢ** records từ bảng ddzl với các bộ lọc tùy chọn (không phân trang).

### 2. GET /api/ddzl/paginated
Lấy danh sách records từ bảng ddzl với các bộ lọc tùy chọn **CÓ PHÂN TRANG** (khuyến nghị cho performance).

#### Query Parameters (GET /api/ddzl)
| Parameter | Type | Required | Description | Example |
|-----------|------|----------|-------------|---------|
| `buynoPrefix` | string | No | Lọc theo 6 ký tự đầu tiên của BUYNO | `202502` |
| `fromDate` | string | No | Ngày bắt đầu (YYYY-MM-DD) | `2025-02-01` |
| `toDate` | string | No | Ngày kết thúc (YYYY-MM-DD) | `2025-02-28` |
| `ddbh` | string | No | Lọc DDBH với LIKE pattern (tự động thêm %) | `Y2505` |
| `article` | string | No | Lọc chính xác theo Article | `164225C` |

#### Query Parameters (GET /api/ddzl/paginated)
| Parameter | Type | Required | Description | Example |
|-----------|------|----------|-------------|---------|
| `buynoPrefix` | string | No | Lọc theo 6 ký tự đầu tiên của BUYNO | `202502` |
| `fromDate` | string | No | Ngày bắt đầu (YYYY-MM-DD) | `2025-02-01` |
| `toDate` | string | No | Ngày kết thúc (YYYY-MM-DD) | `2025-02-28` |
| `ddbh` | string | No | Lọc DDBH với LIKE pattern (tự động thêm %) | `Y2505` |
| `article` | string | No | Lọc chính xác theo Article | `164225C` |
| `page` | int | No | Số trang (bắt đầu từ 1, mặc định: 1) | `1` |
| `pageSize` | int | No | Số records mỗi trang (mặc định: 50, tối đa: 1000) | `50` |

#### Response Format (GET /api/ddzl - No Pagination)
```json
{
  "code": 200,
  "data": [
    {
      "ddbh": "********",
      "gsbh": "GS001",
      "xiexing": "Boot",
      "shehao": "SH001",
      "article": "164225C",
      "ccgb": "CC001",
      "khbh": "KH001",
      "bb": "BB001",
      "khpo": "PO001",
      "version": 1,
      "trader": "John Doe",
      "traderpo": "TPO001",
      "ddlb": "Type1",
      "ddzt": "Active",
      "cplb": "Product1",
      "bzfs": "Standard",
      "dest": "Vietnam",
      "ddgb": "DG001",
      "ddrq": "2025-02-15T10:30:00Z",
      "jytj": "Term1",
      "fktj": "Payment1",
      "shipDate": "2025-03-01T00:00:00Z",
      "pairs": 1000,
      "totals": 1000,
      "zlbh": "ZL001",
      "gsdh": "GS001",
      "cfno": "CF001",
      "userid": "user123",
      "userdate": "2025-02-15T10:30:00Z",
      "ddcz": "Create",
      "ddpacksm": "Standard Pack",
      "labelcharge": "LC001",
      "gender": "Unisex",
      "yn": "Y",
      "ordermode": "Normal",
      "okdate": "2025-02-16T00:00:00Z",
      "buyno": "202502001234",
      "pairs2": 1000,
      "balance2": 0.0,
      "flag": "Active",
      "rytype": "Type1"
    }
  ],
  "message": "success"
}
```

#### Response Format (GET /api/ddzl/paginated - With Pagination)
```json
{
  "code": 200,
  "data": {
    "data": [
      {
        "ddbh": "********",
        "gsbh": "GS001",
        "xiexing": "Boot",
        "shehao": "SH001",
        "article": "164225C",
        "ccgb": "CC001",
        "khbh": "KH001",
        "bb": "BB001",
        "khpo": "PO001",
        "version": 1,
        "trader": "John Doe",
        "traderpo": "TPO001",
        "ddlb": "Type1",
        "ddzt": "Active",
        "cplb": "Product1",
        "bzfs": "Standard",
        "dest": "Vietnam",
        "ddgb": "DG001",
        "ddrq": "2025-02-15T10:30:00Z",
        "jytj": "Term1",
        "fktj": "Payment1",
        "shipDate": "2025-03-01T00:00:00Z",
        "pairs": 1000,
        "totals": 1000,
        "zlbh": "ZL001",
        "gsdh": "GS001",
        "cfno": "CF001",
        "userid": "user123",
        "userdate": "2025-02-15T10:30:00Z",
        "ddcz": "Create",
        "ddpacksm": "Standard Pack",
        "labelcharge": "LC001",
        "gender": "Unisex",
        "yn": "Y",
        "ordermode": "Normal",
        "okdate": "2025-02-16T00:00:00Z",
        "buyno": "202502001234",
        "pairs2": 1000,
        "balance2": 0.0,
        "flag": "Active",
        "rytype": "Type1"
      }
    ],
    "totalRows": 15420,
    "totalPages": 309,
    "page": 1,
    "pageSize": 50,
    "hasNext": true,
    "hasPrev": false,
    "message": "success",
    "success": true
  },
  "message": "success"
}
```

#### Error Response
```json
{
  "code": 400,
  "data": null,
  "message": "Invalid request parameters"
}
```

---

## 🔍 Filter Logic

API này được thiết kế để khớp chính xác với câu truy vấn SQL Server:

```sql
SELECT * FROM ddzl
WHERE 1 = 1
AND SUBSTRING(BUYNO,1,6) = '202502'  --BuyNO
AND USERDATE BETWEEN '2025/02/01' AND '2025/02/28'  --Date
AND DDBH LIKE 'Y2505%'   --OrderNo
AND Article = '164225C'  --SKU
ORDER BY USERDATE DESC
```

### Filter Details:
- **BUYNO Filter**: `SUBSTRING(BUYNO, 1, 6) = 'value'`
- **Date Range**: `USERDATE BETWEEN 'fromDate' AND 'toDate'`
- **DDBH Filter**: `DDBH LIKE 'value%'` (tự động thêm %)
- **Article Filter**: `Article = 'value'` (exact match)
- **Sorting**: Kết quả được sắp xếp theo `USERDATE DESC`

---

## 💻 Frontend Integration Examples

### JavaScript/Fetch
```javascript
// Get all records
const getAllDDZL = async () => {
  try {
    const response = await fetch('http://localhost:8081/api/ddzl');
    const data = await response.json();
    
    if (data.code === 200) {
      console.log('Records:', data.data);
      return data.data;
    } else {
      console.error('Error:', data.message);
    }
  } catch (error) {
    console.error('Network error:', error);
  }
};

// Get filtered records
const getFilteredDDZL = async (filters) => {
  const params = new URLSearchParams();
  
  if (filters.buynoPrefix) params.append('buynoPrefix', filters.buynoPrefix);
  if (filters.fromDate) params.append('fromDate', filters.fromDate);
  if (filters.toDate) params.append('toDate', filters.toDate);
  if (filters.ddbh) params.append('ddbh', filters.ddbh);
  if (filters.article) params.append('article', filters.article);
  
  try {
    const response = await fetch(`http://localhost:8081/api/ddzl?${params}`);
    const data = await response.json();
    
    if (data.code === 200) {
      return data.data;
    } else {
      throw new Error(data.message);
    }
  } catch (error) {
    console.error('Error:', error);
    throw error;
  }
};

// Usage examples
getAllDDZL();

getFilteredDDZL({
  buynoPrefix: '202502',
  fromDate: '2025-02-01',
  toDate: '2025-02-28',
  ddbh: 'Y2505',
  article: '164225C'
});

// Get paginated records (RECOMMENDED for performance)
const getPaginatedDDZL = async (filters, page = 1, pageSize = 50) => {
  const params = new URLSearchParams();

  if (filters.buynoPrefix) params.append('buynoPrefix', filters.buynoPrefix);
  if (filters.fromDate) params.append('fromDate', filters.fromDate);
  if (filters.toDate) params.append('toDate', filters.toDate);
  if (filters.ddbh) params.append('ddbh', filters.ddbh);
  if (filters.article) params.append('article', filters.article);

  // Add pagination parameters
  params.append('page', page.toString());
  params.append('pageSize', pageSize.toString());

  try {
    const response = await fetch(`http://localhost:8081/api/ddzl/paginated?${params}`);
    const result = await response.json();

    if (result.code === 200) {
      return {
        data: result.data.data,
        totalRows: result.data.totalRows,
        totalPages: result.data.totalPages,
        currentPage: result.data.page,
        pageSize: result.data.pageSize,
        hasNext: result.data.hasNext,
        hasPrev: result.data.hasPrev
      };
    } else {
      throw new Error(result.message);
    }
  } catch (error) {
    console.error('Error:', error);
    throw error;
  }
};

// Usage examples for pagination
getPaginatedDDZL({
  buynoPrefix: '202502',
  fromDate: '2025-02-01',
  toDate: '2025-02-28'
}, 1, 50); // Page 1, 50 records per page

getPaginatedDDZL({}, 2, 100); // Page 2, 100 records per page, no filters
```

### Axios
```javascript
import axios from 'axios';

const api = axios.create({
  baseURL: 'http://localhost:8081/api',
  timeout: 10000
});

// Get all DDZL records
export const getAllDDZL = async () => {
  try {
    const response = await api.get('/ddzl');
    return response.data.data;
  } catch (error) {
    console.error('API Error:', error.response?.data?.message || error.message);
    throw error;
  }
};

// Get filtered DDZL records
export const getFilteredDDZL = async (filters) => {
  try {
    const response = await api.get('/ddzl', { params: filters });
    return response.data.data;
  } catch (error) {
    console.error('API Error:', error.response?.data?.message || error.message);
    throw error;
  }
};
```

### Vue.js Composition API
```vue
<template>
  <div>
    <!-- Filter Form -->
    <form @submit.prevent="fetchData">
      <input v-model="filters.buynoPrefix" placeholder="BUYNO Prefix (6 chars)" />
      <input v-model="filters.fromDate" type="date" />
      <input v-model="filters.toDate" type="date" />
      <input v-model="filters.ddbh" placeholder="DDBH" />
      <input v-model="filters.article" placeholder="Article" />
      <button type="submit">Search</button>
      <button type="button" @click="clearFilters">Clear</button>
    </form>

    <!-- Results -->
    <div v-if="loading">Loading...</div>
    <div v-else-if="error">Error: {{ error }}</div>
    <div v-else>
      <p>Found {{ records.length }} records</p>
      <table>
        <thead>
          <tr>
            <th>DDBH</th>
            <th>BUYNO</th>
            <th>Article</th>
            <th>User Date</th>
            <th>Pairs</th>
          </tr>
        </thead>
        <tbody>
          <tr v-for="record in records" :key="record.ddbh">
            <td>{{ record.ddbh }}</td>
            <td>{{ record.buyno }}</td>
            <td>{{ record.article }}</td>
            <td>{{ formatDate(record.userdate) }}</td>
            <td>{{ record.pairs }}</td>
          </tr>
        </tbody>
      </table>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted } from 'vue'
import axios from 'axios'

const records = ref([])
const loading = ref(false)
const error = ref(null)

const filters = ref({
  buynoPrefix: '',
  fromDate: '',
  toDate: '',
  ddbh: '',
  article: ''
})

const api = axios.create({
  baseURL: 'http://localhost:8081/api'
})

const fetchData = async () => {
  loading.value = true
  error.value = null
  
  try {
    // Remove empty filters
    const params = Object.fromEntries(
      Object.entries(filters.value).filter(([_, v]) => v !== '')
    )
    
    const response = await api.get('/ddzl', { params })
    records.value = response.data.data
  } catch (err) {
    error.value = err.response?.data?.message || err.message
  } finally {
    loading.value = false
  }
}

const clearFilters = () => {
  filters.value = {
    buynoPrefix: '',
    fromDate: '',
    toDate: '',
    ddbh: '',
    article: ''
  }
  fetchData()
}

const formatDate = (dateString) => {
  return new Date(dateString).toLocaleString()
}

onMounted(() => {
  fetchData()
})
</script>
```

---

## 🧪 Testing URLs

```bash
# Health check
GET http://localhost:8081/api/ping

# Get all records
GET http://localhost:8081/api/ddzl

# Filter by BUYNO prefix
GET http://localhost:8081/api/ddzl?buynoPrefix=202502

# Filter by date range
GET http://localhost:8081/api/ddzl?fromDate=2025-02-01&toDate=2025-02-28

# Filter by DDBH
GET http://localhost:8081/api/ddzl?ddbh=Y2505

# Filter by Article
GET http://localhost:8081/api/ddzl?article=164225C

# Combined filters (matching SQL Server query)
GET http://localhost:8081/api/ddzl?buynoPrefix=202502&fromDate=2025-02-01&toDate=2025-02-28&ddbh=Y2505&article=164225C

# PAGINATED ENDPOINTS (RECOMMENDED for performance)

# Get first page with default page size (50)
GET http://localhost:8081/api/ddzl/paginated

# Get specific page with custom page size
GET http://localhost:8081/api/ddzl/paginated?page=2&pageSize=100

# Paginated with filters
GET http://localhost:8081/api/ddzl/paginated?buynoPrefix=202502&page=1&pageSize=25

# Paginated with date range
GET http://localhost:8081/api/ddzl/paginated?fromDate=2025-02-01&toDate=2025-02-28&page=1&pageSize=50

# Paginated with all filters
GET http://localhost:8081/api/ddzl/paginated?buynoPrefix=202502&fromDate=2025-02-01&toDate=2025-02-28&ddbh=Y2505&article=164225C&page=1&pageSize=20
```

---

## ⚠️ Important Notes

1. **No Pagination**: API trả về TẤT CẢ kết quả phù hợp
2. **Date Format**: Sử dụng `YYYY-MM-DD` format cho fromDate và toDate
3. **DDBH Pattern**: Tự động thêm `%` vào cuối (LIKE pattern)
4. **Case Sensitive**: Tất cả filters đều case-sensitive
5. **Sorting**: Kết quả luôn được sắp xếp theo USERDATE DESC (mới nhất trước)
6. **Empty Filters**: Nếu không có filter nào, trả về tất cả records

---

## 🔧 Error Handling

```javascript
const handleApiError = (error) => {
  if (error.response) {
    // Server responded with error status
    console.error('API Error:', error.response.data.message);
    return error.response.data.message;
  } else if (error.request) {
    // Network error
    console.error('Network Error:', error.message);
    return 'Network connection failed';
  } else {
    // Other error
    console.error('Error:', error.message);
    return 'An unexpected error occurred';
  }
};
```

---

## 📊 Data Types Reference

| Field | Type | Description |
|-------|------|-------------|
| ddbh | string | Order number |
| buyno | string | Buy number |
| article | string | Product article code |
| userdate | string (ISO 8601) | User creation date |
| pairs | number | Number of pairs |
| totals | number | Total quantity |
| version | number | Version number |
| balance2 | number (float) | Balance amount |

Tất cả date fields được trả về dưới dạng ISO 8601 string format: `"2025-02-15T10:30:00Z"`
