import { defineStore } from 'pinia'
import { ref } from 'vue'

export const useAppStore = defineStore('app', () => {
  const isDarkMode = ref<boolean>(localStorage.getItem('darkMode') === 'true')
  const sidebarCollapsed = ref<boolean>(false)
  const loading = ref<boolean>(false)

  const toggleDarkMode = () => {
    isDarkMode.value = !isDarkMode.value
    localStorage.setItem('darkMode', isDarkMode.value.toString())
    
    // Apply dark mode to document
    if (isDarkMode.value) {
      document.documentElement.classList.add('dark')
    } else {
      document.documentElement.classList.remove('dark')
    }
  }

  const toggleSidebar = () => {
    sidebarCollapsed.value = !sidebarCollapsed.value
  }

  const setLoading = (value: boolean) => {
    loading.value = value
  }

  // Initialize dark mode
  const initTheme = () => {
    if (isDarkMode.value) {
      document.documentElement.classList.add('dark')
    }
  }

  return {
    isDarkMode,
    sidebarCollapsed,
    loading,
    toggleDarkMode,
    toggleSidebar,
    setLoading,
    initTheme
  }
}) 