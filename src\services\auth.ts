import api from './api';
import type { LoginRequest, LoginResponse, UserData } from '@/types/auth.types';

/**
 * Authentication service for login functionality
 */

/**
 * Login user with username and password
 * @param credentials Login credentials
 * @returns Promise with login response
 */
export async function login(credentials: LoginRequest): Promise<UserData> {
  try {
    const response = await api.post<LoginResponse>('/login', credentials);
    
    // Check the response code to determine success/failure
    if (response.data.code === 200) {
      // Check if data is a string (error message) or object (user data)
      if (typeof response.data.data === 'string') {
        // Error case - data contains error message
        throw new Error(response.data.data);
      } else if (response.data.data && typeof response.data.data === 'object' && response.data.data.token) {
        // Success case - data contains user information
        localStorage.setItem('auth-token', response.data.data.token);
        localStorage.setItem('user-data', JSON.stringify(response.data.data));
        
        return response.data.data;
      } else {
        throw new Error('Login failed - invalid response format');
      }
    } else if (response.data.code === 500) {
      // Server error
      throw new Error('Server error');
    } else {
      // Unknown error code
      throw new Error(response.data.message || 'Login failed');
    }
  } catch (error: any) {
    if (error.response) {
      // Server responded with error
      const errorResponse = error.response;
      
      // Handle different HTTP status codes
      if (errorResponse.status === 400) {
        throw new Error(errorResponse.data?.message || 'Tài khoản không tồn tại hoặc đã bị khóa');
      } else if (errorResponse.status === 500) {
        throw new Error('Server error');
      } else {
        throw new Error(errorResponse.data?.message || 'Login failed');
      }
    } else if (error.request) {
      // Network error
      throw new Error('Lỗi kết nối mạng');
    } else {
      // Other error (including our custom errors from above)
      throw new Error(error.message || 'Login failed');
    }
  }
}

/**
 * Logout user
 */
export function logout(): void {
  try {
    // Remove stored data
    localStorage.removeItem('auth-token');
    localStorage.removeItem('user-data');
  } catch (error) {
    // Silent error handling
  }
}

/**
 * Get current user data from localStorage
 * @returns User data or null if not authenticated
 */
export function getCurrentUser(): UserData | null {
  try {
    const userData = localStorage.getItem('user-data');
    if (userData) {
      return JSON.parse(userData);
    }
    return null;
  } catch (error) {
    return null;
  }
}

/**
 * Get current authentication token
 * @returns Token string or null if not authenticated
 */
export function getAuthToken(): string | null {
  try {
    return localStorage.getItem('auth-token');
  } catch (error) {
    return null;
  }
}

/**
 * Check if user is authenticated
 * @returns boolean indicating authentication status
 */
export function isAuthenticated(): boolean {
  const token = getAuthToken();
  const user = getCurrentUser();
  return !!(token && user);
}

/**
 * Clear all authentication data
 */
export function clearAuthData(): void {
  try {
    localStorage.removeItem('auth-token');
    localStorage.removeItem('user-data');
  } catch (error) {
    // Silent error handling
  }
} 