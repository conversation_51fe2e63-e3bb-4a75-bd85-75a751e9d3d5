import axios from 'axios';
import { DEFAULT_API_CONFIG } from '@/types/ddzl-api.types';

const api = axios.create({
  ...DEFAULT_API_CONFIG,
});

// Request interceptor to add auth token
api.interceptors.request.use(
  (config) => {
    const token = localStorage.getItem('auth-token');
    if (token) {
      config.headers.Authorization = `Bearer ${token}`;
    }
    return config;
  },
  (error) => {
    return Promise.reject(error);
  }
);

api.interceptors.response.use(
  (response) => {
    // For login endpoint, don't reject based on code since login can return 400/500 for errors
    // The auth service will handle the response appropriately
    if (response.config.url?.includes('/login')) {
      return response;
    }
    
    // For other endpoints, check if the response has an error code
    if (response.data.code && response.data.code !== 200) {
      return Promise.reject(new Error(response.data.msg || response.data.message || 'Error'));
    }
    // Return the full response so we can access response.data.data
    return response;
  },
  (error) => {
    // Handle authentication errors
    if (error.response?.status === 401) {
      // Token expired or invalid, clear auth data
      localStorage.removeItem('auth-token');
      localStorage.removeItem('user-data');
      console.log('Authentication expired, cleared auth data');
    }
    // You can handle global errors here
    return Promise.reject(error);
  }
);

export default api; 