<template>
  <el-tabs v-model="activeName" class="demo-tabs" @tab-click="handleClick">
    <el-tab-pane label="Monthly Plan" name="MonthlyPlan">
      <div class="header-container">
        <el-row :gutter="12" align="middle">
          <el-col :span="4">
            <el-date-picker
              v-model="month"
              type="month"
              placeholder="Select Month"
              value-format="YYYY/MM"
              style="width: 100%"
            />
          </el-col>

          <el-col :span="3">
            <el-select
              v-model="building"
              placeholder="Building"
              style="width: 100%"
            >
              <el-option label="A01" value="A01" />
              <el-option label="A02" value="A02" />
              <el-option label="A03" value="A03" />
            </el-select>
          </el-col>

          <el-col :span="3">
            <el-select
              v-model="lean"
              placeholder="Lean Line"
              style="width: 100%"
            >
              <el-option label="LEAN01" value="LEAN01" />
              <el-option label="LEAN02" value="LEAN02" />
            </el-select>
          </el-col>

          <el-col :span="6">
            <el-button type="primary" @click="query">Query</el-button>
            <el-button @click="clear">Clear</el-button>
            <el-button @click="refresh"
              >Update Material Arrival Status</el-button
            >
          </el-col>
        </el-row>
        <!-- Row 2: Tùy chọn nâng cao -->
        <el-row :gutter="12" class="mt-2" align="middle">
          <el-col :span="3">
            <el-input-number v-model="leadDays" :min="1" label="Lead Days" />
          </el-col>

          <el-col :span="6">
            <span class="legend green">>= 12 Days</span>
            <span class="legend orange">8 - 11 Days</span>
            <span class="legend red"><= 7 Days</span>
          </el-col>

          <el-col :span="5">
            <el-date-picker
              v-model="statusUpdateDate"
              type="datetime"
              placeholder="Show red mark after"
              style="width: 100%"
            />
          </el-col>

          <el-col :span="4">
            <el-select
              v-model="cellColor"
              placeholder="Cell Color"
              style="width: 100%"
            >
              <el-option label="MATERIAL STATUS" value="MATERIAL" />
              <el-option label="PLAN STATUS" value="PLAN" />
            </el-select>
          </el-col>

          <el-col :span="3">
            <el-select
              v-model="columnWidth"
              placeholder="Column Width"
              style="width: 100%"
            >
              <el-option label="Fit Screen" value="fit" />
              <el-option label="Fixed" value="fixed" />
            </el-select>
          </el-col>
        </el-row>
      </div>
    </el-tab-pane>
    <el-tab-pane label="Shipping Plan" name="ShippingPlan"
      >Shipping Plan</el-tab-pane
    >
  </el-tabs>
</template>

<script setup lang="ts">
import { ref } from "vue";

const month = ref("");
const building = ref("");
const lean = ref("");
const leadDays = ref(12);
const statusUpdateDate = ref("");
const cellColor = ref("MATERIAL");
const columnWidth = ref("fit");

function query() {
  console.log("Query clicked");
}
function clear() {
  month.value = "";
  building.value = "";
  lean.value = "";
}
function refresh() {
  console.log("Refresh clicked");
}
</script>

<style scoped>
.header-container {
  padding: 12px;
  border-radius: 8px;
  box-shadow: 0 1px 4px rgba(0, 0, 0, 0.1);
}
.mt-2 {
  margin-top: 12px;
}
.legend {
  padding: 2px 6px;
  border-radius: 4px;
  margin-right: 6px;
  font-size: 12px;
  font-weight: 500;
}
.legend.green {
  background: #67c23a;
  color: #fff;
}
.legend.orange {
  background: #e6a23c;
  color: #fff;
}
.legend.red {
  background: #f56c6c;
  color: #fff;
}
</style>
