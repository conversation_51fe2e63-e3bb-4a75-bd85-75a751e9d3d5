{"name": "ICANDOIT", "version": "0.0.0", "scripts": {"lint": "eslint \"src/**/*.{js,vue}\"", "dev": "cross-env NODE_ENV=development vite --host", "serve": "cross-env NODE_ENV=development vite --host", "build": "cross-env NODE_ENV=production vite build", "preview": "cross-env vite preview", "build:preview": "vite build --mode production && vite preview", "lint:eslint": "eslint --cache --max-warnings 0  \"{src}/**/*.{vue,ts,tsx}\" --fix", "lint:prettier": "prettier --write  \"src/**/*.{js,json,tsx,css,less,scss,vue,html,md}\"", "lint:style": "stylelint --cache --fix \"**/*.{vue,less,postcss,css,scss}\" --cache --cache-location node_modules/.cache/stylelint/", "lint:lint-staged": "lint-staged -c ./.husky/lintstagedrc.js", "lint:pretty": "pretty-quick --staged"}, "dependencies": {"@element-plus/icons-vue": "^2.3.1", "@fortawesome/fontawesome-free": "^6.6.0", "@fortawesome/fontawesome-svg-core": "^6.7.2", "@fortawesome/free-solid-svg-icons": "^6.7.2", "@fortawesome/vue-fontawesome": "^3.0.8", "@mdi/font": "^7.4.47", "axios": "^1.6.8", "cross-env": "^7.0.3", "element-plus": "^2.8.4", "pinia": "^3.0.3", "roboto-fontface": "*", "three": "^0.171.0", "vue": "^3.4.0", "vue-i18n": "^9.13.1", "vue-router": "^4.4.5", "vue3-datepicker": "^0.4.0", "vuetify": "^3.7.2"}, "devDependencies": {"@mdi/js": "^7.4.47", "@vitejs/plugin-vue": "^5.0.4", "prettier": "^3.3.3", "sass": "^1.71.1", "unplugin-auto-import": "^0.18.3", "unplugin-fonts": "^1.1.1", "unplugin-vue-components": "^0.26.0", "vite": "^6.3.5", "vite-plugin-vuetify": "^2.0.3"}}