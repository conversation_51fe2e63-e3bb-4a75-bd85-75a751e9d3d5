{"info": {"name": "DDZL API Collection", "description": "API collection for DDZL data queries with flexible filtering", "schema": "https://schema.getpostman.com/json/collection/v2.1.0/collection.json"}, "variable": [{"key": "baseUrl", "value": "http://localhost:8081/api", "type": "string"}], "item": [{"name": "Health Check", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/ping", "host": ["{{baseUrl}}"], "path": ["ping"]}, "description": "Check if API server is running"}, "response": []}, {"name": "Get All DDZL Records", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/ddzl", "host": ["{{baseUrl}}"], "path": ["ddzl"]}, "description": "Get all DDZL records without any filters"}, "response": []}, {"name": "Filter by BUYNO Prefix", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/ddzl?buynoPrefix=202502", "host": ["{{baseUrl}}"], "path": ["ddzl"], "query": [{"key": "buynoPrefix", "value": "202502", "description": "First 6 characters of BUYNO"}]}, "description": "Filter records by BUYNO prefix (first 6 characters)"}, "response": []}, {"name": "Filter by Date Range", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/ddzl?fromDate=2025-02-01&toDate=2025-02-28", "host": ["{{baseUrl}}"], "path": ["ddzl"], "query": [{"key": "fromDate", "value": "2025-02-01", "description": "Start date (YYYY-MM-DD)"}, {"key": "toDate", "value": "2025-02-28", "description": "End date (YYYY-MM-DD)"}]}, "description": "Filter records by USERDATE range"}, "response": []}, {"name": "Filter by DDBH", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/ddzl?ddbh=Y2505", "host": ["{{baseUrl}}"], "path": ["ddzl"], "query": [{"key": "ddbh", "value": "Y2505", "description": "DDBH pattern (automatically adds % at the end)"}]}, "description": "Filter records by DDBH with LIKE pattern"}, "response": []}, {"name": "Filter by Article", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/ddzl?article=164225C", "host": ["{{baseUrl}}"], "path": ["ddzl"], "query": [{"key": "article", "value": "164225C", "description": "Exact match for Article"}]}, "description": "Filter records by exact Article match"}, "response": []}, {"name": "Combined Filters (SQL Server Query)", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/ddzl?buynoPrefix=202502&fromDate=2025-02-01&toDate=2025-02-28&ddbh=Y2505&article=164225C", "host": ["{{baseUrl}}"], "path": ["ddzl"], "query": [{"key": "buynoPrefix", "value": "202502", "description": "BUYNO prefix"}, {"key": "fromDate", "value": "2025-02-01", "description": "Start date"}, {"key": "toDate", "value": "2025-02-28", "description": "End date"}, {"key": "ddbh", "value": "Y2505", "description": "DDBH pattern"}, {"key": "article", "value": "164225C", "description": "Article code"}]}, "description": "Matches SQL Server query: SELECT * FROM ddzl WHERE SUBSTRING(BUYNO,1,6) = '202502' AND USERDATE BETWEEN '2025/02/01' AND '2025/02/28' AND DDBH LIKE 'Y2505%' AND Article = '164225C' ORDER BY USERDATE DESC"}, "response": []}, {"name": "Filter by Date Only (From)", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/ddzl?fromDate=2025-02-01", "host": ["{{baseUrl}}"], "path": ["ddzl"], "query": [{"key": "fromDate", "value": "2025-02-01", "description": "Records from this date onwards"}]}, "description": "Filter records from a specific date onwards"}, "response": []}, {"name": "Filter by Date Only (To)", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/ddzl?toDate=2025-02-28", "host": ["{{baseUrl}}"], "path": ["ddzl"], "query": [{"key": "toDate", "value": "2025-02-28", "description": "Records up to this date"}]}, "description": "Filter records up to a specific date"}, "response": []}, {"name": "Multiple Filters Example", "request": {"method": "GET", "header": [], "url": {"raw": "{{baseUrl}}/ddzl?buynoPrefix=202502&ddbh=Y2505", "host": ["{{baseUrl}}"], "path": ["ddzl"], "query": [{"key": "buynoPrefix", "value": "202502", "description": "BUYNO prefix"}, {"key": "ddbh", "value": "Y2505", "description": "DDBH pattern"}]}, "description": "Example of combining multiple filters"}, "response": []}], "event": [{"listen": "prerequest", "script": {"type": "text/javascript", "exec": ["// Set current date for testing", "pm.globals.set('currentDate', new Date().toISOString().split('T')[0]);"]}}, {"listen": "test", "script": {"type": "text/javascript", "exec": ["// Basic response validation", "pm.test('Status code is 200', function () {", "    pm.response.to.have.status(200);", "});", "", "pm.test('Response has correct structure', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData).to.have.property('code');", "    pm.expect(jsonData).to.have.property('data');", "    pm.expect(jsonData).to.have.property('message');", "});", "", "pm.test('Response code is 200', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.code).to.equal(200);", "});", "", "pm.test('Data is array', function () {", "    const jsonData = pm.response.json();", "    pm.expect(jsonData.data).to.be.an('array');", "});", "", "// Log response info", "console.log('Response time:', pm.response.responseTime + 'ms');", "console.log('Records count:', pm.response.json().data.length);"]}}]}