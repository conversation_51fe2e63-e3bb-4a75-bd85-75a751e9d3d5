import axios from 'axios'
import { DEFAULT_API_CONFIG } from '@/types/ddzl-api.types';

const api = axios.create({
  ...DEFAULT_API_CONFIG,
});


export function fetchModels(params?: Record<string, any>) {
  return api.get('/xxzl-model', { params })
}

export function fetchModelsPaginated(params?: Record<string, any>) {
  return api.get('/xxzl-model/paginated', { params })
}

export function createModel(data: { article: string; model: string }) {
  return api.post('/xxzl-model', data)
}

export function fetchDistinctArticles() {
  return api.get('/xxzl-model/articles')
}

export function fetchDistinctModels() {
  return api.get('/xxzl-model/models')
} 