/* eslint-disable */
/* prettier-ignore */
// @ts-nocheck
// Generated by unplugin-vue-components
// Read more: https://github.com/vuejs/core/pull/3399
export {}

declare module 'vue' {
  export interface GlobalComponents {
    AddxxzlmodelDialog: typeof import('./src/components/xxzlModel/AddxxzlmodelDialog.vue')['default']
    AppLayout: typeof import('./src/components/layout/AppLayout.vue')['default']
    CustomHeader: typeof import('./src/components/layout/CustomHeader.vue')['default']
    CustomMain: typeof import('./src/components/layout/CustomMain.vue')['default']
    CustomSidebar: typeof import('./src/components/layout/CustomSidebar.vue')['default']
    DarkModeToggle: typeof import('./src/components/DarkModeToggle.vue')['default']
    RouterLink: typeof import('vue-router')['RouterLink']
    RouterView: typeof import('vue-router')['RouterView']
  }
}
